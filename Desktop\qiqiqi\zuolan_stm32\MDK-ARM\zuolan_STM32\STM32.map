Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to fmc.o(i.MX_FMC_Init) for MX_FMC_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to cmd_to_fun.o(i.CTRL_INIT) for CTRL_INIT
    main.o(i.main) refers to da_output.o(i.DA_Init) for DA_Init
    main.o(i.main) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    main.o(i.main) refers to app_pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to my_fft.o(i.fft_init) for fft_init
    main.o(i.main) refers to ad9959.o(i.AD9959_Init) for AD9959_Init
    main.o(i.main) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to my_usart.o(.data) for rxTemp1
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for .bss
    fmc.o(i.HAL_FMC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.HAL_FMC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(i.HAL_FMC_MspInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(i.HAL_SRAM_MspDeInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspInit) refers to fmc.o(i.HAL_FMC_MspInit) for HAL_FMC_MspInit
    fmc.o(i.MX_FMC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(i.MX_FMC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    fmc.o(i.MX_FMC_Init) refers to fmc.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.NMI_Handler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    ad_measure.o(i.ad_proc) refers to ad_measure.o(i.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(i.readFIFOData) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(i.setSamplingFrequency) refers to cmd_to_fun.o(i.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(i.setSamplingFrequency) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.setSamplingFrequency) for setSamplingFrequency
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(i.vpp_adc_parallel) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.readFIFOData) for readFIFOData
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.findMinMax) for findMinMax
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.data) for .data
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.bss) for .bss
    ad9959.o(i.AD9959_CS_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_CS_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Ch) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_IO_UpDate) refers to ad9959.o(i.AD9959_UP_L) for AD9959_UP_L
    ad9959.o(i.AD9959_IO_UpDate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_IO_UpDate) refers to ad9959.o(i.AD9959_UP_H) for AD9959_UP_H
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_PDC_L) for AD9959_PDC_L
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_Start) for AD9959_Start
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_Reset) for AD9959_Reset
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_P0_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P0_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P1_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P1_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P2_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P2_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P3_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P3_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_PDC_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_PDC_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_RST_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_RST_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Reset) refers to ad9959.o(i.AD9959_RST_L) for AD9959_RST_L
    ad9959.o(i.AD9959_Reset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_Reset) refers to ad9959.o(i.AD9959_RST_H) for AD9959_RST_H
    ad9959.o(i.AD9959_SCK_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SCK_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO0_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO0_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO1_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO1_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO2_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO2_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO3_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO3_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Fre) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959_Set_Fre) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959_Set_Fre) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Pha) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Pha) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Fre) for AD9959_Set_Fre
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Pha) for AD9959_Set_Pha
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Amp) for AD9959_Set_Amp
    ad9959.o(i.AD9959_Start) refers to ad9959.o(i.AD9959_CS_H) for AD9959_CS_H
    ad9959.o(i.AD9959_Start) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_Start) refers to ad9959.o(i.AD9959_CS_L) for AD9959_CS_L
    ad9959.o(i.AD9959_Sweep_Phase) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959_Sweep_Phase) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959_Sweep_Phase) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Sweep_Phase) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Sweep_Phase) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_UP_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_UP_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SDIO0_H) for AD9959_SDIO0_H
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SDIO0_L) for AD9959_SDIO0_L
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SCK_H) for AD9959_SCK_H
    ad9959.o(i.AD9959_WByte) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SCK_L) for AD9959_SCK_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_SDIO3_L) for AD9959_SDIO3_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_CS_L) for AD9959_CS_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_WByte) for AD9959_WByte
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_CS_H) for AD9959_CS_H
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_SDIO3_H) for AD9959_SDIO3_H
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(.data) for .data
    ad9959.o(i.AD9959__Sweep_Amp) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959__Sweep_Amp) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959__Sweep_Fre) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959__Sweep_Fre) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959__Sweep_Fre) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P3_L) for AD9959_P3_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P3_H) for AD9959_P3_H
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P0_L) for AD9959_P0_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P0_H) for AD9959_P0_H
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P1_L) for AD9959_P1_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P1_H) for AD9959_P1_H
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P2_L) for AD9959_P2_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P2_H) for AD9959_P2_H
    ad9959.o(i.AD9959_proc) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.AD9959_proc) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_proc) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    ad9959.o(i.AD9959_proc) refers to ad9959.o(.data) for .data
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(i.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_START) for DA_FPGA_START
    da_output.o(i.DA_Apply_Settings) refers to da_output.o(.bss) for .bss
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.DA_SetConfig) refers to da_output.o(.bss) for .bss
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(i.fre_measure) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad1) for fre_measure_ad1
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad2) for fre_measure_ad2
    key_app.o(i.get_current_ad_frequency) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_proc) refers to my_fft.o(i.configure_da_output_from_peaks) for configure_da_output_from_peaks
    key_app.o(i.key_proc) refers to my_usart.o(i.my_printf) for my_printf
    key_app.o(i.key_proc) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    key_app.o(i.key_proc) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to da_output.o(.bss) for da_channels
    key_app.o(i.key_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.set_current_ad_frequency) refers to key_app.o(.data) for .data
    my_fft.o(i.calculate_fft_spectrum) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(i.calculate_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_precise_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_thd) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.configure_da_output_from_peaks) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    my_fft.o(i.configure_da_output_from_peaks) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.configure_da_output_from_peaks) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.configure_da_output_from_peaks) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    my_fft.o(i.configure_da_output_from_peaks) refers to my_fft.o(.data) for .data
    my_fft.o(i.configure_da_output_from_peaks) refers to usart.o(.bss) for huart1
    my_fft.o(i.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(i.fft_init) refers to my_fft.o(i.generate_hanning_window) for generate_hanning_window
    my_fft.o(i.fft_init) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.find_dual_peaks) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(i.find_dual_peaks) refers to my_fft.o(i.find_spectrum_peaks) for find_spectrum_peaks
    my_fft.o(i.find_dual_peaks) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_fft.o(i.find_spectrum_peaks) refers to my_fft.o(i.calculate_precise_frequency) for calculate_precise_frequency
    my_fft.o(i.find_spectrum_peaks) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(i.generate_hanning_window) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.get_peak1_frequency) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_peak1_magnitude) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_peak2_frequency) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_peak2_magnitude) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_precise_peak_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_dual_peaks_info) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_dual_peaks_info) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.output_dual_peaks_info) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_dual_peaks_info) refers to my_fft.o(.data) for .data
    my_fft.o(i.output_dual_peaks_info) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_dual_peaks_info) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_fft_spectrum) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.get_precise_peak_frequency) for get_precise_peak_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd) for calculate_thd
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.find_dual_peaks) for find_dual_peaks
    my_fft.o(i.output_fft_spectrum) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.output_dual_peaks_info) for output_dual_peaks_info
    my_fft.o(i.output_fft_spectrum) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.perform_dual_peak_analysis) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(i.calculate_fft_spectrum) for calculate_fft_spectrum
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(i.find_dual_peaks) for find_dual_peaks
    my_fft.o(i.perform_dual_peak_analysis) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(i.output_dual_peaks_info) for output_dual_peaks_info
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_filter.o(i.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(i.arm_fir_f32_lp) refers to my_filter.o(.constdata) for .constdata
    phase_measure.o(i.calculate_phase_diff) refers to phase_measure.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman) refers to kalman.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(.bss) for .bss
    kalman.o(i.kalman_thd) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman_thd) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman_thd) refers to kalman.o(.data) for .data
    kalman.o(i.kalman_thd) refers to kalman.o(.bss) for .bss
    my_hmi.o(i.HMI_Send_Float) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Float) refers to dflti.o(.text) for __aeabi_i2d
    my_hmi.o(i.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(i.HMI_Send_Float) refers to f2d.o(.text) for __aeabi_f2d
    my_hmi.o(i.HMI_Send_Float) refers to dmul.o(.text) for __aeabi_dmul
    my_hmi.o(i.HMI_Send_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    my_hmi.o(i.HMI_Send_Float) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Int) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Int) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_String) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_String) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Wave_Clear) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Wave_Clear) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hmi.o(i.HMI_Write_Wave_Low) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Low) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart_pack.o(i.ParseFrame) for ParseFrame
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    my_usart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.ParseFrame) refers to my_usart_pack.o(i.ParseDataToVariables) for ParseDataToVariables
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.SendFrame) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.data) for .data
    hmi_key_handler.o(i.DDS_Update_Display) refers to f2d.o(.text) for __aeabi_f2d
    hmi_key_handler.o(i.DDS_Update_Display) refers to printfa.o(i.__0sprintf) for __2sprintf
    hmi_key_handler.o(i.DDS_Update_Display) refers to memcpya.o(.text) for __aeabi_memcpy4
    hmi_key_handler.o(i.DDS_Update_Display) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    hmi_key_handler.o(i.DDS_Update_Display) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.DDS_Update_Display) refers to usart.o(.bss) for huart2
    hmi_key_handler.o(i.HMI_Clear_Key_Status) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to memcpya.o(.text) for __aeabi_memcpy4
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to usart.o(.bss) for huart2
    hmi_key_handler.o(i.HMI_Get_DDS_Amplitude) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DDS_Frequency) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DDS_Status) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DDS_Waveform) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_Key_Status) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_Get_Key_Status) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.HMI_Key_Process) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Key_Process) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Key_Process) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_Key_Process) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Key_Process) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.HMI_Key_Test) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Key_Test) refers to hmi_key_handler.o(i.HMI_Key_Process) for HMI_Key_Process
    hmi_key_handler.o(i.HMI_Key_Test) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hmi_key_handler.o(i.HMI_Key_Test) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.HMI_Key_Test) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.HMI_Key_Test) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_Set_DDS_Amplitude) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Amplitude) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_DDS_Amplitude) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Set_DDS_Frequency) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Frequency) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_DDS_Frequency) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Set_DDS_Waveform) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Waveform) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_DDS_Waveform) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(.data) refers to hmi_key_handler.o(.conststring) for .conststring
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(i.uart_proc) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.uart_proc) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.uart_proc) refers to ad_measure.o(.data) for vol_amp2
    scheduler.o(i.uart_proc) refers to app_pid.o(.data) for output
    scheduler.o(i.uart_proc) refers to usart.o(.bss) for huart1
    scheduler.o(.data) refers to ad_measure.o(i.ad_proc) for ad_proc
    scheduler.o(.data) refers to key_app.o(i.key_proc) for key_proc
    scheduler.o(.data) refers to hmi_key_handler.o(i.HMI_Key_Process) for HMI_Key_Process
    app_pid.o(i.PID_Init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to app_pid.o(i.increment_pid_ctrl) for increment_pid_ctrl
    app_pid.o(i.Pid_Proc) refers to ad_measure.o(.data) for vol_amp2
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.data) for .data
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to ad9959.o(.data) for pid_vin
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) for FLASH_OB_DisablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) for FLASH_OB_EnablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to fmc.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to fmc.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (40 bytes).
    Removing fmc.o(.rev16_text), (4 bytes).
    Removing fmc.o(.revsh_text), (4 bytes).
    Removing fmc.o(.rrx_text), (6 bytes).
    Removing fmc.o(i.HAL_SRAM_MspDeInit), (92 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (124 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(.rev16_text), (4 bytes).
    Removing ad_measure.o(.revsh_text), (4 bytes).
    Removing ad_measure.o(.rrx_text), (6 bytes).
    Removing ad9959.o(.rev16_text), (4 bytes).
    Removing ad9959.o(.revsh_text), (4 bytes).
    Removing ad9959.o(.rrx_text), (6 bytes).
    Removing ad9959.o(i.AD9959_P0_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P0_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P1_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P1_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P2_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P2_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P3_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P3_L), (16 bytes).
    Removing ad9959.o(i.AD9959_PDC_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO1_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO1_L), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO2_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO2_L), (16 bytes).
    Removing ad9959.o(i.AD9959_Sweep_Phase), (220 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Amp), (148 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Fre), (260 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Trigger), (90 bytes).
    Removing ad9959.o(i.AD9959_proc), (84 bytes).
    Removing ad9959.o(.data), (44 bytes).
    Removing ad9959.o(.data), (1 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing da_output.o(.rev16_text), (4 bytes).
    Removing da_output.o(.revsh_text), (4 bytes).
    Removing da_output.o(.rrx_text), (6 bytes).
    Removing da_output.o(.data), (4 bytes).
    Removing freq_measure.o(.rev16_text), (4 bytes).
    Removing freq_measure.o(.revsh_text), (4 bytes).
    Removing freq_measure.o(.rrx_text), (6 bytes).
    Removing freq_measure.o(i.fre_measure), (192 bytes).
    Removing freq_measure.o(i.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(i.fre_measure_ad2), (20 bytes).
    Removing freq_measure.o(i.freq_proc), (14 bytes).
    Removing freq_measure.o(.data), (24 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(i.get_current_ad_frequency), (12 bytes).
    Removing key_app.o(i.set_current_ad_frequency), (12 bytes).
    Removing key_app.o(.data), (4 bytes).
    Removing my_fft.o(.rev16_text), (4 bytes).
    Removing my_fft.o(.revsh_text), (4 bytes).
    Removing my_fft.o(.rrx_text), (6 bytes).
    Removing my_fft.o(i.calculate_fft_spectrum), (204 bytes).
    Removing my_fft.o(i.calculate_precise_frequency), (160 bytes).
    Removing my_fft.o(i.calculate_thd), (248 bytes).
    Removing my_fft.o(i.find_dual_peaks), (158 bytes).
    Removing my_fft.o(i.find_spectrum_peaks), (336 bytes).
    Removing my_fft.o(i.get_peak1_frequency), (12 bytes).
    Removing my_fft.o(i.get_peak1_magnitude), (12 bytes).
    Removing my_fft.o(i.get_peak2_frequency), (12 bytes).
    Removing my_fft.o(i.get_peak2_magnitude), (12 bytes).
    Removing my_fft.o(i.get_precise_peak_frequency), (208 bytes).
    Removing my_fft.o(i.output_dual_peaks_info), (324 bytes).
    Removing my_fft.o(i.output_fft_spectrum), (704 bytes).
    Removing my_fft.o(i.perform_dual_peak_analysis), (76 bytes).
    Removing my_fft.o(i.round_to_nearest_k), (36 bytes).
    Removing my_filter.o(.rev16_text), (4 bytes).
    Removing my_filter.o(.revsh_text), (4 bytes).
    Removing my_filter.o(.rrx_text), (6 bytes).
    Removing my_filter.o(i.arm_fir_f32_lp), (40 bytes).
    Removing my_filter.o(.constdata), (4 bytes).
    Removing my_filter.o(.constdata), (204 bytes).
    Removing phase_measure.o(.rev16_text), (4 bytes).
    Removing phase_measure.o(.revsh_text), (4 bytes).
    Removing phase_measure.o(.rrx_text), (6 bytes).
    Removing phase_measure.o(i.calculate_phase_diff), (208 bytes).
    Removing phase_measure.o(.data), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rrx_text), (6 bytes).
    Removing kalman.o(i.Kalman_init), (48 bytes).
    Removing kalman.o(i.kalman), (96 bytes).
    Removing kalman.o(i.kalman_filter), (82 bytes).
    Removing kalman.o(i.kalman_thd), (64 bytes).
    Removing kalman.o(.bss), (308 bytes).
    Removing kalman.o(.data), (4 bytes).
    Removing my_hmi.o(.rev16_text), (4 bytes).
    Removing my_hmi.o(.revsh_text), (4 bytes).
    Removing my_hmi.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(i.HMI_Send_Float), (188 bytes).
    Removing my_hmi.o(i.HMI_Send_Int), (120 bytes).
    Removing my_hmi.o(i.HMI_Wave_Clear), (120 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Fast), (180 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Low), (128 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(.data), (2 bytes).
    Removing my_usart.o(.data), (4 bytes).
    Removing my_usart.o(.data), (4 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart_pack.o(.rev16_text), (4 bytes).
    Removing my_usart_pack.o(.revsh_text), (4 bytes).
    Removing my_usart_pack.o(.rrx_text), (6 bytes).
    Removing my_usart_pack.o(i.PrepareFrame), (232 bytes).
    Removing my_usart_pack.o(i.SendFrame), (24 bytes).
    Removing my_usart_pack.o(i.SetParseTemplate), (52 bytes).
    Removing hmi_key_handler.o(.rev16_text), (4 bytes).
    Removing hmi_key_handler.o(.revsh_text), (4 bytes).
    Removing hmi_key_handler.o(.rrx_text), (6 bytes).
    Removing hmi_key_handler.o(i.HMI_Clear_Key_Status), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_DDS_Display_Init), (244 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Amplitude), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Frequency), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Status), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Waveform), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_Key_Status), (28 bytes).
    Removing hmi_key_handler.o(i.HMI_Key_Test), (140 bytes).
    Removing hmi_key_handler.o(i.HMI_Set_DDS_Amplitude), (48 bytes).
    Removing hmi_key_handler.o(i.HMI_Set_DDS_Frequency), (56 bytes).
    Removing hmi_key_handler.o(i.HMI_Set_DDS_Waveform), (44 bytes).
    Removing cmd_to_fun.o(.rev16_text), (4 bytes).
    Removing cmd_to_fun.o(.revsh_text), (4 bytes).
    Removing cmd_to_fun.o(.rrx_text), (6 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_START), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_STOP), (30 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.uart_proc), (76 bytes).
    Removing scheduler.o(.data), (4 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.Pid_Proc), (80 bytes).
    Removing app_pid.o(i.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.data), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit), (32 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (112 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue), (38 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Start), (104 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA), (268 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop), (26 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (86 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (110 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (28 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (68 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (68 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (220 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (108 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (684 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (448 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (52 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (136 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (116 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (52 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (62 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (372 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (42 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (34 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (98 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2980 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (52 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (126 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (106 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (492 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (236 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (310 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (74 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (148 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (124 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (100 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (56 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (184 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (232 bytes).
    Removing stm32f4xx_ll_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_AttributeSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_CommonSpace_Timing_Init), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_DeInit), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC), (110 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_Init), (88 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_AttributeSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_CommonSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_IOSpace_Timing_Init), (28 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_Init), (38 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus), (22 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init), (94 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate), (12 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand), (80 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber), (12 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init), (134 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable), (16 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (32 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (64 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (64 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (64 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (76 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (50 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (42 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (150 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (100 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (256 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (212 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (26 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (148 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (148 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32), (340 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32), (64 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32), (890 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32), (858 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_f32), (190 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).
    Removing cdcmple.o(.text), (48 bytes).

639 unused section(s) (total 913177 bytes) removed from the image.
