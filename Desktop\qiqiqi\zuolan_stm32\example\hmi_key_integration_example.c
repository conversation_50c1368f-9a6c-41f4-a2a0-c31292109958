/**
 * @file hmi_key_integration_example.c
 * @brief 串口屏按键处理模块集成示例
 * @details 展示如何在主程序中集成和使用串口屏按键处理功能
 * <AUTHOR>
 * @date 2025-07-24
 */

#include "bsp_system.h"
#include "hmi_key_handler.h"
#include "my_hmi.h"
#include "key_app.h"

/**
 * @brief 主程序示例 - 展示如何集成串口屏按键处理
 * @details 这是一个完整的主程序示例，展示了如何在系统中集成串口屏按键处理功能
 * @param None
 * @retval None
 */
void main_program_example(void)
{
    // 系统初始化
    my_printf(&huart1, "系统启动中...\r\n");

    // 启动串口2中断接收（用于串口屏按键和数据包）
    HAL_UART_Receive_IT(&huart2, &rxTemp2, 1);

    // 显示启动信息到串口屏
    HMI_Send_String(huart2, "t0", "System Ready");
    HMI_Send_String(huart2, "t1", "Press Key 1-5");

    my_printf(&huart1, "系统初始化完成\r\n");
    my_printf(&huart1, "串口屏按键功能已启用\r\n");
    my_printf(&huart1, "支持按键：1(开启DDS), 2(暂停DDS), 3(频率+100Hz), 4(频率-100Hz), 5(AD波形显示)\r\n");

    // 主循环
    uint32_t last_info_update = 0;
    uint32_t last_key_check = 0;

    while(1)
    {
        uint32_t current_tick = HAL_GetTick();

        // 每10ms检查一次串口屏按键
        if(current_tick - last_key_check >= 10)
        {
            HMI_Key_Process(); // 处理串口屏按键
            last_key_check = current_tick;
        }

        // 每20ms处理一次硬件按键
        if(current_tick % 20 == 0)
        {
            key_proc(); // 处理硬件按键
        }

        // 每100ms更新一次信号信息显示
        if(current_tick - last_info_update >= 100)
        {
            HMI_Display_Signal_Info(); // 更新信号信息显示
            last_info_update = current_tick;
        }

        // 其他系统任务
        // ad_proc();           // AD采集处理
        // DA_Apply_Settings(); // DA输出处理
        // 其他任务...

        HAL_Delay(1); // 1ms基础延时
    }
}

/**
 * @brief 串口屏按键功能扩展示例
 * @details 展示如何扩展串口屏按键功能，添加自定义功能
 * @param None
 * @retval None
 */
void hmi_key_extension_example(void)
{
    my_printf(&huart1, "=== 串口屏按键功能扩展示例 ===\r\n");

    // 检查是否有按键按下
    uint8_t key_status = HMI_Get_Key_Status();

    if(key_status != 0)
    {
        switch(key_status)
        {
            case HMI_KEY_1: // 按键1：开启DDS输出
            {
                my_printf(&huart1, "按键1：开启DDS输出\r\n");
                // DDS输出功能已在HMI_Key_Process()中实现
                HMI_Send_String(huart2, "t2", "DDS ON");
                break;
            }

            case HMI_KEY_2: // 按键2：暂停DDS输出
            {
                my_printf(&huart1, "按键2：暂停DDS输出\r\n");
                // DDS暂停功能已在HMI_Key_Process()中实现
                HMI_Send_String(huart2, "t2", "DDS OFF");
                break;
            }

            case HMI_KEY_3: // 按键3：频率步进+100Hz
            {
                my_printf(&huart1, "按键3：频率增加100Hz\r\n");
                // 频率增加功能已在HMI_Key_Process()中实现
                uint32_t freq = HMI_Get_DDS_Frequency();
                my_printf(&huart1, "当前频率：%luHz\r\n", freq);
                break;
            }

            case HMI_KEY_4: // 按键4：频率步进-100Hz
            {
                my_printf(&huart1, "按键4：频率减少100Hz\r\n");
                // 频率减少功能已在HMI_Key_Process()中实现
                uint32_t freq = HMI_Get_DDS_Frequency();
                my_printf(&huart1, "当前频率：%luHz\r\n", freq);
                break;
            }

            case HMI_KEY_5: // 按键5：已定义为与硬件按键2一致
            {
                my_printf(&huart1, "按键5：显示AD波形（与硬件按键2一致）\r\n");
                // 这个功能已在HMI_Key_Process()中实现
                break;
            }

            default:
            {
                my_printf(&huart1, "未知按键：%c\r\n", key_status);
                break;
            }
        }

        // 清除按键状态
        HMI_Clear_Key_Status();
    }
}

/**
 * @brief 串口屏按键与硬件按键协同工作示例
 * @details 展示串口屏按键和硬件按键如何协同工作
 * @param None
 * @retval None
 */
void hmi_hardware_key_cooperation_example(void)
{
    my_printf(&huart1, "=== 串口屏按键与硬件按键协同示例 ===\r\n");

    static uint32_t last_check = 0;
    uint32_t current_tick = HAL_GetTick();

    // 每50ms检查一次
    if(current_tick - last_check >= 50)
    {
        // 处理硬件按键
        key_proc();

        // 处理串口屏按键
        HMI_Key_Process();

        // 检查串口屏按键5和硬件按键2的协同
        uint8_t hmi_key = HMI_Get_Key_Status();
        if(hmi_key == HMI_KEY_5)
        {
            my_printf(&huart1, "串口屏按键5触发，执行与硬件按键2相同的功能\r\n");
            // 功能已在HMI_Key_Process()中自动处理
            HMI_Clear_Key_Status();
        }

        last_check = current_tick;
    }
}

/**
 * @brief 串口屏按键状态监控示例
 * @details 展示如何监控和记录串口屏按键的使用情况
 * @param None
 * @retval None
 */
void hmi_key_monitoring_example(void)
{
    static uint32_t key_press_count[6] = {0}; // 按键1-5的按下次数统计
    static uint32_t last_report = 0;

    uint32_t current_tick = HAL_GetTick();

    // 检查按键状态
    uint8_t key_status = HMI_Get_Key_Status();
    if(key_status >= '1' && key_status <= '5')
    {
        uint8_t key_index = key_status - '0'; // 转换为数字索引
        key_press_count[key_index]++;

        my_printf(&huart1, "按键%c被按下，累计次数：%lu\r\n",
                  key_status, key_press_count[key_index]);

        HMI_Clear_Key_Status();
    }

    // 每10秒报告一次统计信息
    if(current_tick - last_report >= 10000)
    {
        my_printf(&huart1, "=== 按键使用统计 ===\r\n");
        for(uint8_t i = 1; i <= 5; i++)
        {
            my_printf(&huart1, "按键%d: %lu次\r\n", i, key_press_count[i]);
        }
        my_printf(&huart1, "==================\r\n");

        last_report = current_tick;
    }
}

/**
 * @brief 完整的系统集成示例
 * @details 展示一个完整的系统，包含所有功能的集成
 * @param None
 * @retval None
 */
void complete_system_integration_example(void)
{
    my_printf(&huart1, "========================================\r\n");
    my_printf(&huart1, "    完整系统集成示例启动\r\n");
    my_printf(&huart1, "========================================\r\n");

    // 系统初始化
    HAL_UART_Receive_IT(&huart2, &rxTemp2, 1); // 启动串口2接收

    // 显示系统信息
    HMI_Send_String(huart2, "t0", "System Online");
    HMI_Send_String(huart2, "t1", "Ready for Input");

    my_printf(&huart1, "系统功能说明：\r\n");
    my_printf(&huart1, "硬件按键1: 切换DA波形\r\n");
    my_printf(&huart1, "硬件按键2: 显示AD波形\r\n");
    my_printf(&huart1, "串口屏按键1: 开启DDS输出\r\n");
    my_printf(&huart1, "串口屏按键2: 暂停DDS输出\r\n");
    my_printf(&huart1, "串口屏按键3: 频率步进+100Hz\r\n");
    my_printf(&huart1, "串口屏按键4: 频率步进-100Hz\r\n");
    my_printf(&huart1, "串口屏按键5: 与硬件按键2功能一致\r\n");
    my_printf(&huart1, "========================================\r\n");

    // 主循环（示例运行30秒）
    uint32_t start_time = HAL_GetTick();
    while(HAL_GetTick() - start_time < 30000)
    {
        // 核心功能处理
        key_proc();                    // 硬件按键处理
        HMI_Key_Process();            // 串口屏按键处理
        HMI_Display_Signal_Info();    // 信号信息显示

        // 扩展功能示例
        hmi_key_extension_example();
        hmi_key_monitoring_example();

        HAL_Delay(20); // 20ms循环周期
    }

    my_printf(&huart1, "完整系统集成示例结束\r\n");
}
