# 串口屏DDS控制功能说明

## 功能概述

本模块为串口屏按键处理系统新增了DDS（直接数字频率合成器）控制功能，通过串口屏按键可以实时控制AD9959 DDS芯片的输出状态和频率。

## 按键功能映射

| 按键 | 功能 | 说明 |
|------|------|------|
| 按键1 | 开启DDS输出 | 启用DDS输出，使用当前设置的频率、幅度和相位 |
| 按键2 | 暂停DDS输出 | 关闭DDS输出（将幅度设为0） |
| 按键3 | 频率步进+100Hz | 将当前频率增加100Hz |
| 按键4 | 频率步进-100Hz | 将当前频率减少100Hz |
| 按键5 | AD波形显示 | 保持原有功能，显示AD波形到串口屏 |

## 技术参数

- **频率范围**: 100Hz ~ 50MHz
- **频率步进**: 100Hz
- **默认频率**: 1MHz
- **默认通道**: 通道1
- **默认幅度**: 1023（最大值）
- **默认相位**: 0度

## 使用方法

### 1. 基本操作流程

1. **开启DDS输出**: 按下串口屏按键1
2. **调整频率**: 使用按键3（增加）或按键4（减少）来调整频率
3. **暂停输出**: 按下串口屏按键2
4. **重新开启**: 再次按下按键1

### 2. 频率控制

- 每次按下按键3，频率增加100Hz
- 每次按下按键4，频率减少100Hz
- 频率变化会实时应用到DDS输出（如果输出已开启）
- 频率会自动限制在100Hz到50MHz范围内

### 3. 状态显示

- 串口屏会实时显示DDS状态和当前频率
- 串口1会输出详细的操作日志
- 频率显示格式：
  - 小于1kHz: 显示为Hz
  - 1kHz~1MHz: 显示为kHz
  - 大于1MHz: 显示为MHz

## 代码结构

### 主要文件

- `MY_Communication/Src/hmi_key_handler.c` - 串口屏按键处理主文件
- `MY_Communication/Inc/hmi_key_handler.h` - 头文件声明
- `example/hmi_key_integration_example.c` - 使用示例

### 核心函数

```c
// DDS控制函数
static void DDS_Enable_Output(void);      // 开启DDS输出
static void DDS_Disable_Output(void);     // 关闭DDS输出
static void DDS_Increase_Frequency(void); // 增加频率100Hz
static void DDS_Decrease_Frequency(void); // 减少频率100Hz
static void DDS_Update_Display(void);     // 更新串口屏显示

// 公共接口函数
uint8_t HMI_Get_DDS_Status(void);         // 获取DDS输出状态
uint32_t HMI_Get_DDS_Frequency(void);     // 获取当前频率
void HMI_Set_DDS_Frequency(uint32_t freq); // 设置频率
```

### 全局变量

```c
static uint8_t dds_output_enabled = 0;   // DDS输出使能状态
static uint32_t dds_frequency = 1000000; // DDS输出频率（默认1MHz）
static uint8_t dds_channel = 1;          // DDS输出通道（默认通道1）
static uint16_t dds_amplitude = 1023;    // DDS输出幅度（默认最大值）
static float dds_phase = 0.0f;           // DDS输出相位（默认0度）
```

## 集成说明

### 1. 调度器集成

DDS控制功能已集成到系统调度器中：

```c
static task_t scheduler_task[] = {
    {ad_proc, 1, 0},                    // ADC采样处理任务，每1ms执行一次
    {key_proc, 10, 0},                  // 硬件按键扫描处理任务，每10ms执行一次
    {HMI_Key_Process, 10, 0},           // 串口屏按键处理任务，每10ms执行一次
    {HMI_Display_Signal_Info, 100, 0},  // HMI显示信号信息任务，每100ms执行一次
};
```

### 2. 初始化要求

确保在使用DDS控制功能前已完成以下初始化：

```c
// 在main函数中
HAL_UART_Receive_IT(&huart2, &rxTemp2, 1); // 启动串口2中断接收
AD9959_Init();                              // 初始化AD9959芯片
scheduler_init();                           // 初始化调度器
```

### 3. 主循环集成

```c
while(1) {
    scheduler_run(); // 运行调度器，自动处理串口屏按键
}
```

## 注意事项

1. **频率范围**: 请确保设置的频率在AD9959芯片的有效范围内
2. **硬件连接**: 确保AD9959芯片与STM32的SPI连接正常
3. **串口配置**: 确保串口2已正确配置用于串口屏通信
4. **中断处理**: 串口屏按键检测依赖串口2的中断接收
5. **实时性**: DDS参数变化会立即应用到硬件输出

## 扩展功能

可以通过修改代码实现以下扩展功能：

1. **可变步进**: 修改频率步进值（当前固定为100Hz）
2. **多通道控制**: 支持控制AD9959的多个输出通道
3. **幅度控制**: 添加幅度调节功能
4. **相位控制**: 添加相位调节功能
5. **预设频率**: 添加常用频率的快速设置功能

## 故障排除

### 常见问题

1. **按键无响应**: 检查串口2中断是否正常启动
2. **DDS无输出**: 检查AD9959硬件连接和初始化
3. **频率不准确**: 检查AD9959的参考时钟配置
4. **串口屏显示异常**: 检查串口2的波特率和数据格式

### 调试方法

1. 通过串口1查看详细的操作日志
2. 使用示波器检查DDS输出信号
3. 检查串口屏的通信协议是否正确
4. 验证AD9959寄存器配置是否正确

## 版本信息

- **版本**: V1.0
- **作者**: 左岚
- **日期**: 2025-07-30
- **兼容性**: STM32F429 + AD9959 + 串口屏
