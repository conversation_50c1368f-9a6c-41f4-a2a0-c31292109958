/**
 * @file hmi_key_integration_example.h
 * @brief 串口屏按键处理模块集成示例头文件
 * @details 声明了串口屏按键处理功能的集成示例函数
 * <AUTHOR>
 * @date 2025-07-24
 */

#ifndef __HMI_KEY_INTEGRATION_EXAMPLE_H__
#define __HMI_KEY_INTEGRATION_EXAMPLE_H__

#include "stm32f4xx_hal.h"
#include "stdint.h"

// 示例函数声明

/**
 * @brief 主程序示例 - 展示如何集成串口屏按键处理
 * @param None
 * @retval None
 */
void main_program_example(void);

/**
 * @brief 串口屏按键功能扩展示例
 * @param None
 * @retval None
 */
void hmi_key_extension_example(void);

/**
 * @brief 串口屏按键与硬件按键协同工作示例
 * @param None
 * @retval None
 */
void hmi_hardware_key_cooperation_example(void);

/**
 * @brief 串口屏按键状态监控示例
 * @param None
 * @retval None
 */
void hmi_key_monitoring_example(void);

/**
 * @brief 完整的系统集成示例
 * @param None
 * @retval None
 */
void complete_system_integration_example(void);

#endif // __HMI_KEY_INTEGRATION_EXAMPLE_H__
