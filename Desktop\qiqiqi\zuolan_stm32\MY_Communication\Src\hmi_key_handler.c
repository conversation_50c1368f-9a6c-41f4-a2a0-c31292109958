/**
 * @file hmi_key_handler.c
 * @brief 串口屏按键处理模块
 * @details 该模块负责处理来自串口屏的按键数据，实现按键功能映射
 * <AUTHOR>
 * @date 2025-07-24
 */

#include "hmi_key_handler.h"
#include "my_usart.h"
#include "my_hmi.h"
#include "key_app.h"
#include "bsp_system.h"

// DDS控制相关变量
static uint8_t dds_output_enabled = 0;  // DDS输出使能状态：0=关闭，1=开启
static uint32_t dds_frequency = 1000000; // DDS输出频率，默认1MHz
static uint8_t dds_channel = 1;          // DDS输出通道，默认通道1
static uint16_t dds_amplitude = 1023;    // DDS输出幅度，默认最大值
static float dds_phase = 0.0f;           // DDS输出相位，默认0度

// DDS控制函数声明
static void DDS_Enable_Output(void);     // 开启DDS输出
static void DDS_Disable_Output(void);    // 关闭DDS输出
static void DDS_Increase_Frequency(void); // 增加频率100Hz
static void DDS_Decrease_Frequency(void); // 减少频率100Hz
static void DDS_Update_Display(void);    // 更新串口屏显示

/**
 * @brief 串口屏按键处理主函数
 * @details 检测串口屏按键数据并执行相应功能
 * @param None
 * @retval None
 */
void HMI_Key_Process(void)
{
    if (USART_RX_STA & 0x8000) // 检查是否接收到数据
    {
        uint16_t len = USART_RX_STA & 0x3fff; // 获取数据长度

        switch (USART_RX_BUF[0])
        {
            case '1': // 按键1：开启DDS输出
            {
                DDS_Enable_Output();
                my_printf(&huart1, "HMI按键1：DDS输出已开启，频率=%luHz\r\n", dds_frequency);
                break;
            }

            case '2': // 按键2：暂停DDS输出
            {
                DDS_Disable_Output();
                my_printf(&huart1, "HMI按键2：DDS输出已暂停\r\n");
                break;
            }

            case '3': // 按键3：步进100Hz
            {
                DDS_Increase_Frequency();
                my_printf(&huart1, "HMI按键3：频率增加100Hz，当前频率=%luHz\r\n", dds_frequency);
                break;
            }

            case '4': // 按键4：减100Hz
            {
                DDS_Decrease_Frequency();
                my_printf(&huart1, "HMI按键4：频率减少100Hz，当前频率=%luHz\r\n", dds_frequency);
                break;
            }

            case '5': // 按键5：与硬件按键2功能一致（保持原功能）
            {
                // 调用硬件按键2的功能：显示AD1和AD2波形到HMI串口屏
                HMI_Display_AD_Waveforms();

                // 通过串口1输出AD1数据（与硬件按键2一致）
                for(int i = 0; i < FIFO_SIZE; i++)
                {
                    my_printf(&huart1, "%.4f\r\n", fifo_data1_f[i]);
                }

                // 发送提示信息到串口屏和串口1
                HMI_Send_String(huart2, "t0", "AD Waveforms");
                my_printf(&huart1, "HMI按键5：AD波形已显示到串口屏\r\n");
                break;
            }

            default:
            {
                my_printf(&huart1, "未知HMI按键：%c\r\n", USART_RX_BUF[0]);
                break;
            }
        }

        USART_RX_STA = 0; // 清除接收状态标志
    }
}

/**
 * @brief 串口屏按键测试函数
 * @details 用于测试串口屏按键功能
 * @param None
 * @retval None
 */
void HMI_Key_Test(void)
{
    my_printf(&huart1, "串口屏按键测试开始...\r\n");

    // 模拟按键1-5的测试
    for(uint8_t key = '1'; key <= '5'; key++)
    {
        USART_RX_BUF[0] = key;
        USART_RX_STA = 0x8001; // 设置接收完成标志

        HMI_Key_Process(); // 处理按键

        HAL_Delay(1000); // 延时1秒
    }

    my_printf(&huart1, "串口屏按键测试完成\r\n");
}

/**
 * @brief 获取当前按键状态
 * @details 返回当前按下的按键值
 * @param None
 * @retval uint8_t 按键值('1'-'5')，无按键返回0
 */
uint8_t HMI_Get_Key_Status(void)
{
    if (USART_RX_STA & 0x8000)
    {
        return USART_RX_BUF[0];
    }
    return 0;
}

/**
 * @brief 清除按键状态
 * @details 清除当前按键接收状态
 * @param None
 * @retval None
 */
void HMI_Clear_Key_Status(void)
{
    USART_RX_STA = 0;
}

// ==================== DDS控制函数实现 ====================

/**
 * @brief 开启DDS输出
 * @details 启用DDS输出并更新串口屏显示
 */
static void DDS_Enable_Output(void)
{
    dds_output_enabled = 1;

    // 配置DDS输出
    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);

    // 更新串口屏显示
    DDS_Update_Display();

    // 发送状态到串口屏
    HMI_Send_String(huart2, "t0", "DDS ON");
}

/**
 * @brief 关闭DDS输出
 * @details 禁用DDS输出并更新串口屏显示
 */
static void DDS_Disable_Output(void)
{
    dds_output_enabled = 0;

    // 关闭DDS输出（设置幅度为0）
    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, 0);

    // 更新串口屏显示
    DDS_Update_Display();

    // 发送状态到串口屏
    HMI_Send_String(huart2, "t0", "DDS OFF");
}

/**
 * @brief 增加DDS频率100Hz
 * @details 将当前频率增加100Hz，如果输出已开启则立即应用
 */
static void DDS_Increase_Frequency(void)
{
    dds_frequency += 100; // 增加100Hz

    // 频率上限检查（最大50MHz）
    if (dds_frequency > 50000000) {
        dds_frequency = 50000000;
    }

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 减少DDS频率100Hz
 * @details 将当前频率减少100Hz，如果输出已开启则立即应用
 */
static void DDS_Decrease_Frequency(void)
{
    // 频率下限检查（最小100Hz）
    if (dds_frequency > 100) {
        dds_frequency -= 100; // 减少100Hz
    }

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 更新串口屏显示
 * @details 将当前DDS状态和频率显示到串口屏
 */
static void DDS_Update_Display(void)
{
    char freq_str[32];
    char status_str[32];

    // 格式化频率字符串
    if (dds_frequency >= 1000000) {
        sprintf(freq_str, "%.2fMHz", dds_frequency / 1000000.0f);
    } else if (dds_frequency >= 1000) {
        sprintf(freq_str, "%.2fkHz", dds_frequency / 1000.0f);
    } else {
        sprintf(freq_str, "%luHz", dds_frequency);
    }

    // 格式化状态字符串
    sprintf(status_str, "%s %s", dds_output_enabled ? "ON" : "OFF", freq_str);

    // 发送到串口屏
    HMI_Send_String(huart2, "t1", status_str);
}

// ==================== 公共接口函数 ====================

/**
 * @brief 获取DDS输出状态
 * @details 返回当前DDS输出是否开启
 * @param None
 * @retval uint8_t 1=开启，0=关闭
 */
uint8_t HMI_Get_DDS_Status(void)
{
    return dds_output_enabled;
}

/**
 * @brief 获取DDS当前频率
 * @details 返回当前设置的DDS频率值
 * @param None
 * @retval uint32_t 频率值（Hz）
 */
uint32_t HMI_Get_DDS_Frequency(void)
{
    return dds_frequency;
}

/**
 * @brief 设置DDS频率
 * @details 直接设置DDS频率值
 * @param frequency 频率值（Hz）
 * @retval None
 */
void HMI_Set_DDS_Frequency(uint32_t frequency)
{
    // 频率范围检查
    if (frequency < 100) {
        frequency = 100; // 最小100Hz
    } else if (frequency > 50000000) {
        frequency = 50000000; // 最大50MHz
    }

    dds_frequency = frequency;

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}
